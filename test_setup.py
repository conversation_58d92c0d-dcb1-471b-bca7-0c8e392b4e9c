#!/usr/bin/env python3
"""
Simple test script to verify the setup is working correctly.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def test_setup():
    """Test if the setup is configured correctly"""

    print("Testing setup configuration...")

    # Test 1: Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found. Please create it from .env.example")
        return False

    # Test 2: Load environment variables
    load_dotenv()
    api_key = os.getenv('ELEVENLABS_API_KEY')

    if not api_key or api_key == 'your_elevenlabs_api_key_here':
        print("❌ ELEVENLABS_API_KEY not set or still using placeholder value")
        print("   Please edit .env file with your actual API key")
        return False

    print("✅ API key loaded successfully")

    # Test 3: Check if required directories exist
    output_dir = Path("audio_output")
    if not output_dir.exists():
        output_dir.mkdir(exist_ok=True)
        print("✅ Created audio_output directory")
    else:
        print("✅ audio_output directory exists")

    # Test 4: Check if dependencies are installed
    try:
        import requests
        import datasets
        from tqdm import tqdm
        print("✅ Required Python packages are installed")
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("   Run: pip install -r requirements.txt")
        return False

    print("\n🎉 Setup test completed successfully!")
    print("You can now run: python generate_audio.py")
    return True

if __name__ == "__main__":
    test_setup()
