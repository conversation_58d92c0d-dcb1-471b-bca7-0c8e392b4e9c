#!/usr/bin/env python3
"""
Simplified script to generate audio files using ElevenLabs API for Colombian Spanish dataset.
This version handles space issues and works with smaller batches.
"""

import os
import json
import time
import requests
import argparse
from pathlib import Path
from dotenv import load_dotenv
import hashlib

# Load environment variables
load_dotenv()

class SimpleAudioGenerator:
    def __init__(self, voice_id="qHkrJuifPpn95wK3rm2A", max_files=10):
        self.api_key = os.getenv('ELEVENLABS_API_KEY')
        if not self.api_key:
            raise ValueError("ELEVENLABS_API_KEY not found in environment variables. Please create a .env file with your API key.")

        self.voice_id = voice_id
        self.max_files = max_files
        self.base_url = "https://api.elevenlabs.io/v1"
        self.headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.api_key
        }

        # Create output directory
        self.output_dir = Path("audio_output")
        self.output_dir.mkdir(exist_ok=True)

        # Progress tracking
        self.progress_file = Path("progress.json")
        self.completed_files = self.load_progress()

    def load_progress(self):
        """Load progress from file"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return {}

    def save_progress(self):
        """Save progress to file"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.completed_files, f, indent=2)

    def get_audio_hash(self, text):
        """Generate hash for text to create unique filename"""
        return hashlib.md5(text.encode()).hexdigest()[:8]

    def generate_audio(self, text, filename):
        """Generate audio file using ElevenLabs API"""
        url = f"{self.base_url}/text-to-speech/{self.voice_id}"

        data = {
            "text": text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }

        try:
            response = requests.post(url, json=data, headers=self.headers)
            response.raise_for_status()

            with open(filename, 'wb') as f:
                f.write(response.content)

            return True
        except requests.exceptions.RequestException as e:
            print(f"Error generating audio for text: {text[:50]}... Error: {e}")
            return False

    def get_sample_texts(self):
        """Get sample Colombian Spanish texts for testing"""
        return [
            "Hola, ¿cómo estás? Me llamo María y soy de Colombia.",
            "Buenos días, espero que tengas un excelente día.",
            "Me encanta la comida colombiana, especialmente el ajiaco.",
            "Voy a la tienda a comprar frutas frescas para el desayuno.",
            "El clima en Bogotá es muy variable, siempre hay que llevar chaqueta.",
            "Mi familia es muy grande, tengo muchos primos y tíos.",
            "Estoy estudiando medicina en la universidad nacional.",
            "El fútbol es muy importante en Colombia, todos ven los partidos.",
            "Me gusta mucho bailar salsa y cumbia en las fiestas.",
            "Las montañas de Colombia son hermosas, especialmente en Medellín."
        ]

    def process_texts(self):
        """Process sample texts and generate audio files"""
        texts = self.get_sample_texts()

        if self.max_files:
            texts = texts[:self.max_files]

        print(f"Processing {len(texts)} sample texts...")

        processed_count = 0

        for i, text in enumerate(texts):
            # Create unique filename
            audio_hash = self.get_audio_hash(text)
            audio_filename = self.output_dir / f"audio_{audio_hash}.mp3"

            # Skip if already processed
            if str(audio_filename) in self.completed_files:
                continue

            print(f"Generating audio {i+1}/{len(texts)}: {text[:50]}...")

            # Generate audio
            success = self.generate_audio(text, audio_filename)

            if success:
                self.completed_files[str(audio_filename)] = {
                    "text": text,
                    "timestamp": time.time(),
                    "audio_hash": audio_hash
                }
                processed_count += 1

                # Save progress every file
                self.save_progress()
                print(f"✅ Generated: {audio_filename.name}")

            # Rate limiting - be respectful to the API
            time.sleep(1.0)

        # Final save
        self.save_progress()
        print(f"Completed! Processed {processed_count} audio files.")

    def run(self):
        """Main execution method"""
        print("Starting Colombian Spanish audio generation...")
        print(f"Using voice ID: {self.voice_id}")
        if self.max_files:
            print(f"Maximum files to generate: {self.max_files}")
        print(f"Output directory: {self.output_dir.absolute()}")

        # Process sample texts
        self.process_texts()

        print("Audio generation completed!")
        print(f"Total files processed: {len(self.completed_files)}")
        print(f"Output saved to: {self.output_dir}")


def main():
    parser = argparse.ArgumentParser(description="Generate audio files using ElevenLabs API for Colombian Spanish")
    parser.add_argument("-n", "--count", type=int, default=10, help="Maximum number of audio files to generate")
    parser.add_argument("-v", "--voice", type=str, default="qHkrJuifPpn95wK3rm2A", help="ElevenLabs voice ID to use")

    args = parser.parse_args()

    try:
        generator = SimpleAudioGenerator(voice_id=args.voice, max_files=args.count)
        generator.run()
    except ValueError as e:
        print(f"Configuration error: {e}")
        print("Please create a .env file with your ELEVENLABS_API_KEY")
        print("You can use .env.example as a template.")
    except KeyboardInterrupt:
        print("\nInterrupted by user. Progress has been saved.")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
