# Colombian Spanish Audio Generation

This project generates audio files using ElevenLabs API for the Colombian Spanish dataset from HuggingFace.

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Get ElevenLabs API Key:**
   - Sign up at [ElevenLabs](https://elevenlabs.io)
   - Go to your profile and generate an API key
   - Copy `.env.example` to `.env` and add your API key:
   ```bash
   cp .env.example .env
   ```

3. **Edit .env file:**
   ```
   ELEVENLABS_API_KEY=your_actual_api_key_here
   ```

## Usage

### Option 1: Simple Version (Recommended)
Use the simple version that works with sample Colombian Spanish texts:
```bash
python generate_audio_simple.py --count 10
```

### Option 2: Full Dataset Version
Use the full version with the complete dataset (requires more disk space):
```bash
python generate_audio.py --count 100
```

## Command Line Options

Both scripts support these options:
- `-n, --count`: Maximum number of audio files to generate
- `-v, --voice`: ElevenLabs voice ID to use (default: qHkrJuifPpn95wK3rm2A)

## Examples

```bash
# Generate 5 audio files with simple version
python generate_audio_simple.py --count 5

# Generate 50 audio files with full dataset
python generate_audio.py --count 50

# Use a different voice
python generate_audio_simple.py --count 10 --voice your_voice_id_here
```

## Features

- **Resumable**: If the script is interrupted, it will continue from where it left off
- **Progress tracking**: Progress is saved to `progress.json`
- **Rate limiting**: Includes delays to respect API limits
- **Unique filenames**: Uses hash-based filenames to avoid conflicts
- **Two modes**: Simple mode for testing, full mode for complete dataset

## Output

- Audio files are saved in the `audio_output/` directory
- Progress is tracked in `progress.json`
- Each audio file is named `audio_[hash].mp3`

## Voice Used

The default voice ID is: `qHkrJuifPpn95wK3rm2A`

## Dataset

The full version uses the [Google Colombian Spanish dataset](https://huggingface.co/datasets/ylacombe/google-colombian-spanish) from HuggingFace.

## Requirements

- Python 3.7+
- ElevenLabs API key
- Internet connection
- ~100MB of disk space for simple version
- ~2GB of disk space for full dataset version

## Troubleshooting

- If you get API errors, check your API key and rate limits
- If the dataset download fails, ensure you have a stable internet connection and enough disk space
- Progress is automatically saved, so you can safely interrupt and resume
- Use the simple version if you have limited disk space
