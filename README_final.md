# 🎵 Colombian Spanish Audio Generation

Generate audio files using ElevenLabs API for Colombian Spanish texts. This project supports both sample texts and full dataset processing with resumable functionality.

## ✨ Features

- 🎤 **ElevenLabs Integration**: Uses Andrea voice (Colombian Spanish)
- 📊 **Resumable Processing**: Continue from where you left off
- 📥 **Dataset Support**: Use full HuggingFace dataset or sample texts
- 💾 **Progress Tracking**: Automatic progress saving
- ⚡ **Command Line Interface**: Easy parameter control
- 🔄 **Rate Limiting**: Respects API limits

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Install dependencies
pip install -r requirements.txt

# Setup API key
cp .env.example .env
# Edit .env and add your ElevenLabs API key
```

### 2. Test Setup
```bash
python test_setup.py
```

### 3. Generate Audio

**Simple usage (sample texts):**
```bash
python generate_audio_complete.py
```

**Generate specific number of files:**
```bash
python generate_audio_complete.py --count 5
```

**Use full dataset:**
```bash
python generate_audio_complete.py --dataset
```

**Resume from previous run:**
```bash
python generate_audio_complete.py --resume
```

## 📋 Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `-n, --count` | Maximum number of files to generate | `--count 10` |
| `-v, --voice` | ElevenLabs voice ID | `--voice qHkrJuifPpn95wK3rm2A` |
| `-d, --dataset` | Use full dataset instead of samples | `--dataset` |
| `--resume` | Resume from previous progress | `--resume` |

## 🎯 Voice Information

- **Voice ID**: `qHkrJuifPpn95wK3rm2A`
- **Name**: Andrea
- **Accent**: Latin American Spanish
- **Gender**: Female
- **Use Case**: Conversational

## 📁 Project Structure

```
├── generate_audio_complete.py  # Main script
├── requirements.txt            # Python dependencies
├── .env.example               # API key template
├── README_final.md            # This file
├── audio_output/              # Generated audio files
├── progress.json              # Progress tracking
└── test_setup.py              # Setup verification
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file with:
```
ELEVENLABS_API_KEY=your_actual_api_key_here
```

### Dependencies
- `elevenlabs`: Official ElevenLabs Python library
- `python-dotenv`: Environment variable management
- `datasets`: HuggingFace datasets library
- `tqdm`: Progress bars

## 📊 Progress Tracking

The script automatically saves progress to `progress.json`. Each entry contains:
- Text content
- Timestamp
- Audio hash
- Voice information
- Source (dataset/sample)

## 🎵 Sample Texts Included

The script includes 15 sample Colombian Spanish phrases covering:
- Greetings and introductions
- Daily conversations
- Colombian culture references
- Food and traditions
- Weather and geography

## 🔍 Troubleshooting

### Common Issues

1. **API Key Error**
   - Ensure `.env` file exists with correct API key
   - Run `python test_setup.py` to verify

2. **Voice Not Found**
   - Voice ID `qHkrJuifPpn95wK3rm2A` is Andrea (Colombian Spanish)
   - Run `python check_voices.py` to see available voices

3. **Dataset Download Issues**
   - Requires stable internet connection
   - Falls back to sample texts if dataset fails

4. **Rate Limiting**
   - Script includes 1-second delays between requests
   - ElevenLabs has API rate limits

### Testing

```bash
# Test API connection
python test_elevenlabs.py

# Check available voices
python check_voices.py

# Verify setup
python test_setup.py
```

## 📈 Usage Examples

```bash
# Generate 5 audio files with sample texts
python generate_audio_complete.py --count 5

# Use full dataset (may take a long time)
python generate_audio_complete.py --dataset --count 50

# Resume previous session
python generate_audio_complete.py --resume

# Use different voice
python generate_audio_complete.py --voice J4vZAFDEcpenkMp3f3R9 --count 3
```

## 🎉 Success Indicators

- ✅ Audio files created in `audio_output/` directory
- ✅ Progress saved to `progress.json`
- ✅ No errors in console output
- ✅ Files named like `audio_[hash].mp3`

## 📝 Notes

- **Resumable**: Safe to interrupt with Ctrl+C, progress is saved
- **Unique filenames**: Hash-based to avoid conflicts
- **Multilingual model**: Uses `eleven_multilingual_v2` for best Spanish support
- **Professional voice**: Andrea provides natural Colombian Spanish pronunciation

## 🔗 Links

- [ElevenLabs API](https://elevenlabs.io/docs)
- [Dataset Source](https://huggingface.co/datasets/ylacombe/google-colombian-spanish)
- [Voice Information](https://elevenlabs.io/voice-lab)

---

**Happy audio generation! 🎵**
