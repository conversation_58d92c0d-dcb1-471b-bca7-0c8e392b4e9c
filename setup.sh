#!/bin/bash

echo "Setting up Colombian Spanish Audio Generation project..."

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please edit .env file and add your ELEVENLABS_API_KEY"
else
    echo ".env file already exists"
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

echo "Setup complete!"
echo "Next steps:"
echo "1. Edit .env file with your ElevenLabs API key"
echo "2. Run: python generate_audio.py"
