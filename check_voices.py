#!/usr/bin/env python3
"""
Check available voices in ElevenLabs account.
"""

import os
from dotenv import load_dotenv
import elevenlabs

# Load environment variables
load_dotenv()

def check_voices():
    """Check available voices"""
    api_key = os.getenv('ELEVENLABS_API_KEY')
    if not api_key:
        print("❌ ELEVENLABS_API_KEY not found in environment variables")
        return

    try:
        # Set API key
        elevenlabs.set_api_key(api_key)

        print("Checking available voices...")

        # Get all voices
        voices = elevenlabs.voices()

        print(f"Found {len(voices)} voices:")
        print("-" * 50)

        for voice in voices:
            print(f"Name: {voice.name}")
            print(f"Voice ID: {voice.voice_id}")
            print(f"Category: {voice.category}")
            print(f"Labels: {voice.labels}")
            print("-" * 30)

    except Exception as e:
        print(f"❌ Error checking voices: {e}")

if __name__ == "__main__":
    check_voices()
