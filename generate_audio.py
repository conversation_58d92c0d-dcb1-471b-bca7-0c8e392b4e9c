#!/usr/bin/env python3
"""
Script to generate audio files using ElevenLabs API for the Colombian Spanish dataset.
This script is resumable - it will continue from where it left off if interrupted.
"""

import os
import json
import time
import requests
import argparse
from pathlib import Path
from datasets import load_dataset
from dotenv import load_dotenv
from tqdm import tqdm
import hashlib

# Load environment variables
load_dotenv()

class AudioGenerator:
    def __init__(self, voice_id="qHkrJuifPpn95wK3rm2A", max_files=None):
        self.api_key = os.getenv('ELEVENLABS_API_KEY')
        if not self.api_key:
            raise ValueError("ELEVENLABS_API_KEY not found in environment variables. Please create a .env file with your API key.")

        self.voice_id = voice_id
        self.max_files = max_files
        self.base_url = "https://api.elevenlabs.io/v1"
        self.headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.api_key
        }

        # Create output directory
        self.output_dir = Path("audio_output")
        self.output_dir.mkdir(exist_ok=True)

        # Progress tracking
        self.progress_file = Path("progress.json")
        self.completed_files = self.load_progress()

    def load_progress(self):
        """Load progress from file"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return {}

    def save_progress(self):
        """Save progress to file"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.completed_files, f, indent=2)

    def get_audio_hash(self, text):
        """Generate hash for text to create unique filename"""
        return hashlib.md5(text.encode()).hexdigest()[:8]

    def generate_audio(self, text, filename):
        """Generate audio file using ElevenLabs API"""
        url = f"{self.base_url}/text-to-speech/{self.voice_id}"

        data = {
            "text": text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }

        try:
            response = requests.post(url, json=data, headers=self.headers)
            response.raise_for_status()

            with open(filename, 'wb') as f:
                f.write(response.content)

            return True
        except requests.exceptions.RequestException as e:
            print(f"Error generating audio for text: {text[:50]}... Error: {e}")
            return False

    def download_dataset(self):
        """Download the Colombian Spanish dataset from HuggingFace"""
        print("Downloading dataset from HuggingFace...")
        try:
            # Try both configurations and use the first available one
            dataset = None
            for config in ['female', 'male']:
                try:
                    dataset = load_dataset("ylacombe/google-colombian-spanish", config, split="train")
                    print(f"Successfully loaded {config} configuration")
                    break
                except Exception as e:
                    print(f"Failed to load {config} configuration: {e}")
                    continue

            if dataset is None:
                raise Exception("Could not load any dataset configuration")

            return dataset
        except Exception as e:
            print(f"Error downloading dataset: {e}")
            return None

    def process_dataset(self, dataset, batch_size=10):
        """Process the dataset and generate audio files"""
        total_items = len(dataset)
        if self.max_files:
            total_items = min(total_items, self.max_files)

        print(f"Processing {total_items} items from dataset...")

        processed_count = 0

        for i in tqdm(range(0, total_items, batch_size)):
            batch = dataset[i:i+batch_size]

            for item in batch:
                text = item.get('text', '')
                if not text:
                    continue

                # Create unique filename
                audio_hash = self.get_audio_hash(text)
                audio_filename = self.output_dir / f"audio_{audio_hash}.mp3"

                # Skip if already processed
                if str(audio_filename) in self.completed_files:
                    continue

                # Generate audio
                success = self.generate_audio(text, audio_filename)

                if success:
                    self.completed_files[str(audio_filename)] = {
                        "text": text,
                        "timestamp": time.time(),
                        "audio_hash": audio_hash
                    }
                    processed_count += 1

                    # Save progress every 10 files
                    if processed_count % 10 == 0:
                        self.save_progress()
                        print(f"Progress saved. Processed {processed_count}/{total_items} files.")

                # Rate limiting - be respectful to the API
                time.sleep(0.5)

        # Final save
        self.save_progress()
        print(f"Completed! Processed {processed_count} audio files.")

    def run(self):
        """Main execution method"""
        print("Starting Colombian Spanish audio generation...")
        print(f"Using voice ID: {self.voice_id}")
        if self.max_files:
            print(f"Maximum files to generate: {self.max_files}")
        print(f"Output directory: {self.output_dir.absolute()}")

        # Download dataset
        dataset = self.download_dataset()
        if dataset is None:
            print("Failed to download dataset. Exiting.")
            return

        # Process dataset
        self.process_dataset(dataset)

        print("Audio generation completed!")
        print(f"Total files processed: {len(self.completed_files)}")
        print(f"Output saved to: {self.output_dir}")


def main():
    parser = argparse.ArgumentParser(description="Generate audio files using ElevenLabs API for Colombian Spanish dataset")
    parser.add_argument("-n", "--count", type=int, help="Maximum number of audio files to generate")
    parser.add_argument("-v", "--voice", type=str, default="qHkrJuifPpn95wK3rm2A", help="ElevenLabs voice ID to use")

    args = parser.parse_args()

    try:
        generator = AudioGenerator(voice_id=args.voice, max_files=args.count)
        generator.run()
    except ValueError as e:
        print(f"Configuration error: {e}")
        print("Please create a .env file with your ELEVENLABS_API_KEY")
        print("You can use .env.example as a template.")
    except KeyboardInterrupt:
        print("\nInterrupted by user. Progress has been saved.")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
