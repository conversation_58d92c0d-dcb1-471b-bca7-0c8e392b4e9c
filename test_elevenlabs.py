#!/usr/bin/env python3
"""
Simple test to verify ElevenLabs API is working correctly.
"""

import os
from dotenv import load_dotenv
import elevenlabs

# Load environment variables
load_dotenv()

def test_api():
    """Test the ElevenLabs API connection"""
    api_key = os.getenv('ELEVENLABS_API_KEY')
    if not api_key:
        print("❌ ELEVENLABS_API_KEY not found in environment variables")
        return False

    try:
        # Set API key
        elevenlabs.set_api_key(api_key)

        # Test with a simple text
        test_text = "Hola, esto es una prueba."

        print("Testing ElevenLabs API...")
        print(f"Text: {test_text}")
        print("Voice ID: qHkrJuifPpn95wK3rm2A")

        # Generate audio
        audio = elevenlabs.generate(
            text=test_text,
            voice="qHkrJuifPpn95wK3rm2A",
            model="eleven_multilingual_v2"
        )

        # Save to file
        elevenlabs.save(audio, "test_output.mp3")

        print("✅ API test successful!")
        print("✅ Audio saved to test_output.mp3")
        return True

    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

if __name__ == "__main__":
    test_api()
