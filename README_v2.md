# Colombian Spanish Audio Generation

This project generates audio files using ElevenLabs API for the Colombian Spanish dataset from HuggingFace.

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Get ElevenLabs API Key:**
   - Sign up at [ElevenLabs](https://elevenlabs.io)
   - Go to your profile and generate an API key
   - Copy `.env.example` to `.env` and add your API key:
   ```bash
   cp .env.example .env
   ```

3. **Edit .env file:**
   ```
   ELEVENLABS_API_KEY=your_actual_api_key_here
   ```

## Usage

Run the script:
```bash
python generate_audio.py
```

## Command Line Options

- `-n, --count`: Maximum number of audio files to generate
- `-v, --voice`: ElevenLabs voice ID to use (default: qHkrJuifPpn95wK3rm2A)

### Examples:

```bash
# Generate 100 audio files
python generate_audio.py --count 100

# Generate 50 files with a different voice
python generate_audio.py --count 50 --voice your_voice_id_here

# Use short form
python generate_audio.py -n 100 -v your_voice_id_here

# Generate unlimited files (default behavior)
python generate_audio.py
```

## Features

- **Resumable**: If the script is interrupted, it will continue from where it left off
- **Progress tracking**: Progress is saved to `progress.json`
- **Rate limiting**: Includes delays to respect API limits
- **Unique filenames**: Uses hash-based filenames to avoid conflicts
- **Configurable limits**: Set maximum number of files to generate via command line

## Output

- Audio files are saved in the `audio_output/` directory
- Progress is tracked in `progress.json`
- Each audio file is named `audio_[hash].mp3`

## Voice Used

The script uses the voice ID: `qHkrJuifPpn95wK3rm2A`

## Dataset

The script uses the [Google Colombian Spanish dataset](https://huggingface.co/datasets/ylacombe/google-colombian-spanish) from HuggingFace.

## Requirements

- Python 3.7+
- ElevenLabs API key
- Internet connection
- ~2GB of disk space for audio files

## Troubleshooting

- If you get API errors, check your API key and rate limits
- If the dataset download fails, ensure you have a stable internet connection
- Progress is automatically saved, so you can safely interrupt and resume
